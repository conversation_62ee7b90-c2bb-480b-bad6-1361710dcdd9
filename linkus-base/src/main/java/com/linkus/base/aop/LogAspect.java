package com.linkus.base.aop;

import cn.hutool.json.JSONUtil;
import com.linkus.base.shiro.LoginUserHolder;
import com.linkus.base.util.IpUtils;

import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志切面
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

    /**
     * 定义切点 - 拦截所有使用LogAnnotation注解的方法
     */
    @Pointcut("@annotation(com.linkus.base.aop.LogAnnotation)")
    public void logPointcut() {
        // 无注解 记录所有
        // 有注解 记录指定日志
        // 加个链路追踪ID
        // 加success和status
    }

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 环绕通知
     */
    @Around("logPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        // 获取方法签名和注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LogAnnotation logAnnotation = method.getAnnotation(LogAnnotation.class);

        String loginName = LoginUserHolder.getLoginName();
        // 记录日志基本信息
        Map<String, Object> logInfo = new HashMap<>();
        logInfo.put("module", logAnnotation.module());
        logInfo.put("operation", logAnnotation.operation());
        logInfo.put("subsystem", logAnnotation.subsystem());
        logInfo.put("loginName", loginName);
        logInfo.put("method", method.getDeclaringClass().getName() + "." + method.getName());
        logInfo.put("time", new Date());

        // 记录请求信息
        if (request != null) {
            logInfo.put("url", request.getRequestURL().toString());
            logInfo.put("httpMethod", request.getMethod());
            logInfo.put("ip", IpUtils.getIpAddr(request));
        }

        // 记录请求参数
        if (logAnnotation.recordParams()) {
            logInfo.put("params", getRequestParams(joinPoint));
        }

        Object result = null;
        try {
            // 执行原方法
            result = joinPoint.proceed();

            // 记录返回结果
            if (logAnnotation.recordResult() && result != null) {
                logInfo.put("result", JSONUtil.toJsonStr(result));
            }

            // 记录执行时间
            logInfo.put("executionTime", System.currentTimeMillis() - startTime);

            // 记录正常日志
            log.info("操作日志: {}", JSONUtil.toJsonStr(logInfo));

            return result;
        } catch (Throwable e) {
            // 记录异常信息
            logInfo.put("executionTime", System.currentTimeMillis() - startTime);
            logInfo.put("exception", e.getClass().getName());
            logInfo.put("exceptionMessage", e.getMessage());

            // 记录异常日志
            log.error("操作异常: {}", JSONUtil.toJsonStr(logInfo), e);

            throw e;
        } finally {
            mongoTemplate.insert(logInfo, "sysAccessLog");
        }
    }

    /**
     * 获取请求参数
     */
    private Object getRequestParams(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        Object[] paramValues = joinPoint.getArgs();

        Map<String, Object> params = new HashMap<>();
        if (paramNames != null && paramNames.length > 0) {
            for (int i = 0; i < paramNames.length; i++) {
                if (paramValues[i] != null) {
                    params.put(paramNames[i], getProcessedParamValue(paramValues[i]));
                }
            }
        }

        return params;
    }

    /**
     * 处理特殊类型的参数值
     */
    private Object getProcessedParamValue(Object param) {
        if (param == null) {
            return null;
        }

        if (param instanceof HttpServletResponse) {
            return "HttpServletResponse";
        }
        if (param instanceof HttpServletRequest) {
            return "HttpServletRequest";
        }
        if (param instanceof MultipartFile) {
            return "MultipartFile";
        }
        if (param instanceof MultipartFile[]) {
            return "MultipartFile[]";
        }
        if (param instanceof byte[]) {
            return "byte[]";
        }

        return param;
    }
} 
