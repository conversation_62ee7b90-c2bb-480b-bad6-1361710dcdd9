package com.linkus.prdmobile.service.impl;

import com.itextpdf.text.Image;
import com.linkus.prdmobile.conf.FastDFSClient;
import com.linkus.prdmobile.model.*;
import com.linkus.prdmobile.model.VO.DownloadLogReq;
import com.linkus.prdmobile.service.LinkusPrdMobileService;
import com.linkus.prdmobile.utils.*;
import lombok.Synchronized;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.csource.common.NameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class LinkusPrdMobileServiceImpl implements LinkusPrdMobileService {
    private static final Logger log = LoggerFactory.getLogger(LinkusPrdMobileServiceImpl.class);

    @Value("${pageCollName}")
    private String pageCollName;

    @Value("${fileCollName}")
    private String fileCollName;

    @Value("${prdMobileFolderCollName}")
    private String prdMobileFolderCollName;

    @Value("${sendCodeLogCollName}")
    private String sendCodeLogCollName;

    @Value("${downloadVerifyLogCollName}")
    private String downloadVerifyLogCollName;

    @Resource
    private FastDFSClient fastDFSClient;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MspSmsUtils mspSmsUtils;

    /**
     * 短信验证码模板
     */
    private static final String smsVerifyCodeTemp = "产品与解决方案文档下载验证码：%s，有效期10分钟。如非本人操作，请忽略。";

    /**
     * 图片信息集合名称
     */
    private static final String IMAGE_INFO_COLLECTION = "image_info";

    @Override
    public void receptionPage(LinkedHashMap<String, Object> fileBase, LinkedHashMap<String, Object> fileFold,
                              LinkedHashMap<String, Object> sysPageCnfg, List<LinkedHashMap<String, Object>> sysPageFields,
                              LinkedHashMap<String, Object> file, LinkedHashMap<String, Object> pushUser) {

        List<TeSysPageField> teSysPageFields = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        int no = 1;
        for (LinkedHashMap<String, Object> sysPageField : sysPageFields) {
            TeSysPageField teSysPageField = new TeSysPageField();

            teSysPageField.setId(new ObjectId(sysPageField.get("id").toString()));

            teSysPageField.setIsValid((Boolean) sysPageField.get("isValid"));

            TeIdName fileBaseIdName = new TeIdName();
            fileBaseIdName.setCid(new ObjectId(fileBase.get("id").toString()));
            fileBaseIdName.setName(fileBase.get("defName").toString());
            teSysPageField.setFileBase(fileBaseIdName);

            TeIdNamePtIds fileFoldIdNamePtIds = new TeIdNamePtIds();
            fileFoldIdNamePtIds.setCid(new ObjectId(fileFold.get("id").toString()));
            fileFoldIdNamePtIds.setName(fileFold.get("defName").toString());
            List<String> p2SelfIds = (List<String>) fileFold.get("parent2SelfIds");
            List<ObjectId> parent2SelfIds = new ArrayList<>();
            for (String p2SelfId : p2SelfIds) {
                parent2SelfIds.add(new ObjectId(p2SelfId));
            }
            fileFoldIdNamePtIds.setParent2SelfIds(parent2SelfIds);
            teSysPageField.setFileFold(fileFoldIdNamePtIds);

            TeIdName fileIdName = new TeIdName();
            fileIdName.setCid(new ObjectId(file.getOrDefault("id", "").toString()));
            fileIdName.setName(file.getOrDefault("fileName", "").toString());
            teSysPageField.setFile(fileIdName);

            teSysPageField.setVerNo((Integer) sysPageCnfg.get("verNo"));


            LinkedHashMap<String, String> page = (LinkedHashMap<String, String>) sysPageField.get("page");
            TeIdName pageIdName = new TeIdName();
            pageIdName.setCid(new ObjectId(page.get("cid")));
            pageIdName.setName(page.get("name"));
            teSysPageField.setPage(pageIdName);

            Boolean isChapterCpnt = (Boolean) sysPageField.get("chapterCpnt");
            teSysPageField.setIsChapterCpnt(isChapterCpnt);

            LinkedHashMap<String, String> chapter = (LinkedHashMap<String, String>) sysPageField.get("chapter");
            TeIdName chapterIdName = new TeIdName();
            chapterIdName.setCid(new ObjectId(chapter.get("cid")));
            chapterIdName.setName(chapter.get("name"));
            teSysPageField.setChapter(chapterIdName);

            LinkedHashMap<String, String> pageCpnt = (LinkedHashMap<String, String>) sysPageField.get("pageCpnt");
            TeIdNameCn pageCpntIdNameCn = new TeIdNameCn();
            pageCpntIdNameCn.setCid(new ObjectId(pageCpnt.get("cid")));
            pageCpntIdNameCn.setName(pageCpnt.get("name"));
            pageCpntIdNameCn.setCodeName(pageCpnt.get("codeName"));
            teSysPageField.setPageCpnt(pageCpntIdNameCn);

            teSysPageField.setPageCpntJson(sysPageField.get("pageCpntJson").toString());

            teSysPageField.setImage(sysPageField.getOrDefault("image", "").toString());

            teSysPageField.setNo(no++);

            LinkedHashMap<String, String> addUser = (LinkedHashMap<String, String>) sysPageField.get("addUser");
            TeUser teUser = new TeUser();
            teUser.setUserId(new ObjectId(addUser.get("userId")));
            teUser.setUserName(addUser.get("userName"));
            teUser.setLoginName(addUser.get("loginName"));
            teUser.setJobCode(addUser.get("jobCode"));
            teSysPageField.setCreateUser(teUser);

            try {
                teSysPageField.setCreateTime(formatter.parse(sysPageField.get("addDate").toString()));
            } catch (ParseException ignored) {
            }

            TeUser pUser = new TeUser();
            pUser.setUserId(new ObjectId(pushUser.get("id").toString()));
            pUser.setUserName(pushUser.get("userName").toString());
            pUser.setLoginName(pushUser.get("loginName").toString());
            pUser.setJobCode(pushUser.get("jobCode").toString());
            teSysPageField.setPushUser(pUser);

            teSysPageField.setPushTime(date);

            teSysPageFields.add(teSysPageField);
        }
        insertSysPageField(teSysPageFields);

        file.put("_id", new ObjectId(file.get("id").toString()));
        file.remove("id");
        file.put("pushType", "page");
        insertSysPageFile(file);
    }

    @Override
    public void receptionFile(LinkedHashMap<String, Object> fileBase, LinkedHashMap<String, Object> fileFold,
                              LinkedHashMap<String, Object> file, LinkedHashMap<String, Object> pushUser) {

        file.put("_id", new ObjectId(file.get("id").toString()));
        file.remove("id");

        Map<String, Object> pUser = new HashMap<>();
        pUser.put("userId", new ObjectId(pushUser.get("id").toString()));
        pUser.put("userName", pushUser.get("userName").toString());
        pUser.put("loginName", pushUser.get("loginName").toString());
        pUser.put("jobCode", pushUser.get("jobCode").toString());
        file.put("pushUser", pUser);

        file.put("fileBase", fileBase);
        file.put("fileFold", fileFold);
        file.put("pushType", "file");

        insertSysPageFile(file);
    }

    @Override
    public List<TeSysPageField> pagePreview(ObjectId pageId, ObjectId fileId) {
        Query query = new Query();
        if (Objects.nonNull(pageId)) {
            query.addCriteria(Criteria.where("page.cid").is(pageId).and("isValid").is(true));
        } else if (Objects.nonNull(fileId)) {
            query.addCriteria(Criteria.where("file.cid").is(fileId).and("isValid").is(true));
        } else {
            throw new IllegalArgumentException("参数错误");
        }
        query.with(Sort.by(Sort.Order.asc("no")));
        return mongoTemplate.find(query, TeSysPageField.class, pageCollName);
    }

    @Override
    public void prdMobileCancelPush(ObjectId fileId, boolean isValid) {

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(fileId));

        Update update = new Update();
        update.set("isValid", isValid);

        mongoTemplate.updateMulti(query, update, fileCollName);


        Query query1 = new Query();
        query1.addCriteria(Criteria.where("file.cid").is(fileId));

        mongoTemplate.updateMulti(query1, update, pageCollName);
    }

    @Override
    public void insertPrdMobileFolder(Map<String, Object> map) {
        Query query = new Query();
        query.addCriteria(Criteria.where("value").is(map.get("value").toString()));
        mongoTemplate.remove(query, prdMobileFolderCollName);
        mongoTemplate.insert(map, prdMobileFolderCollName);
    }

    @Override
    public Document getFolderTreeAndFiles(ObjectId kmBaseDefId) {
        Query q = new Query();
        q.addCriteria(Criteria.where("kmBaseDef.id").is(kmBaseDefId.toString()));
        Document document = mongoTemplate.findOne(q, Document.class, prdMobileFolderCollName);

        Map<ObjectId, List<Map>> foldFileMap = new HashMap<>();
        Query q1 = new Query();
        q1.addCriteria(Criteria.where("fileBaseDefId").is(kmBaseDefId.toString()));
        List<Document> fileDocs = mongoTemplate.find(q1, Document.class, fileCollName);
        Map<String, String> fileTags = new HashMap<>();
        for (Document fileDoc : fileDocs) {
            // 图书馆推送 file
            fileTags.put(fileDoc.get("_id").toString(), fileDoc.get("tags").toString());

            String fileFolderDefId = fileDoc.getString("fileFolderDefId");
            if (StringUtils.isEmpty(fileFolderDefId)
                    || !"file".equals(fileDoc.getString("pushType"))
                    || Boolean.FALSE.equals(fileDoc.getBoolean("isValid"))) {
                continue;
            }
            List<Map> files = foldFileMap.getOrDefault(new ObjectId(fileFolderDefId), new ArrayList<>());
            foldFileMap.put(new ObjectId(fileFolderDefId), files);
            files.add(fileDoc);
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("fileBase.cid").is(kmBaseDefId).and("isValid").is(true));
        query.with(Sort.by(Sort.Order.asc("no")));
        List<TeSysPageField> sysPageFields = mongoTemplate.find(query, TeSysPageField.class, pageCollName);
        Set<ObjectId> fileIds = new HashSet<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (TeSysPageField sysPageField : sysPageFields) {
            // 产品中心、解决方案中心、产品与解决方案图谱、客户成功故事推送 page
            if (fileIds.contains(sysPageField.getFile().getCid())) {
                continue;
            }
            fileIds.add(sysPageField.getFile().getCid());

            List<Map> files = foldFileMap.getOrDefault(sysPageField.getFileFold().getCid(), new ArrayList<>());
            foldFileMap.put(sysPageField.getFileFold().getCid(), files);

            Map<String, Object> fileMap = new HashMap<>();
            fileMap.put("fileBaseDefId", sysPageField.getFileBase().getCid());
            fileMap.put("fileFolderDefId", sysPageField.getFileFold().getCid());
            fileMap.put("fileName", sysPageField.getFile().getName());
            fileMap.put("id", sysPageField.getFile().getCid());
            fileMap.put("isApplied", null);
            fileMap.put("isPageCnfg", null);
            fileMap.put("isValid", sysPageField.getIsValid());
            fileMap.put("lastModifyTime", null);
            fileMap.put("length", null);
            fileMap.put("srcFileId", sysPageField.getPage().getCid());
            fileMap.put("tags", fileTags.get(sysPageField.getFile().getCid().toString()));
            fileMap.put("uploadDesc", null);
            fileMap.put("uploadTime", formatter.format(sysPageField.getCreateTime()));
            fileMap.put("uploadUser", sysPageField.getCreateUser());
            fileMap.put("jobCode", null);
            files.add(fileMap);

        }

        List<Document> children = document.get("children", List.class);
        if (!CollectionUtils.isEmpty(children)) {
            for (Document child : children) {
                dealFile(child, foldFileMap);
            }
        }
        return document;
    }

    @Override
    public List<Document> fuzzyQueryView(String fileFolderDefId, String desc, String tag) {
        Query q = new Query();
        List<Document> documents = mongoTemplate.find(q, Document.class, prdMobileFolderCollName);
        Document doc = null;
        for (Document document : documents) {
            doc = obtainFolder(document, fileFolderDefId);
            if (!CollectionUtils.isEmpty(doc)) {
                break;
            }
        }

        if (CollectionUtils.isEmpty(doc)) {
            return new ArrayList<>();
        }
        Set<String> fileFolderDefIds = obtainFolderAndChildIds(doc);

        Query q1 = new Query();
        Criteria c = Criteria.where("fileFolderDefId").in(fileFolderDefIds).and("isValid").is(true).and("linkedFileIds").is(null);
        if (!StringUtils.isEmpty(desc)) {
            c.and("fileName").regex(desc);
        }
        if (!StringUtils.isEmpty(tag)) {
            c.and("tags").regex(tag);
        }
        q1.addCriteria(c);
        return mongoTemplate.find(q1, Document.class, fileCollName);
    }

    @Override
    public Map<String, Object> fuzzyQueryFileId(String fileFolderDefId, String keyWord, String fileName) {
        Map<String, Object> result = new HashMap<>();
        result.put("isAuth", true);
        Criteria criteria = Criteria.where("qFileFolderDefId").is(fileFolderDefId);
        Query query = new Query();
        query.addCriteria(criteria);
        if (!StringUtils.isEmpty(fileName)) {
            criteria.and("fileName").is(fileName);
        } else if (!StringUtils.isEmpty(keyWord)) {
            criteria.and("fileName").regex(keyWord);
        } else {
            throw new RuntimeException("参数错误");
        }
        query.with(Sort.by(Sort.Order.desc("uploadTime")));
        Document file = mongoTemplate.findOne(query, Document.class, fileCollName);
        result.put("file", file);
        return result;
    }

    @Override
    public String uploadFile(MultipartFile file, Map<String, Object> fileInfo) {
        Assert.notNull(file, "上传文件不能为空");
        Assert.notNull(fileInfo, "上传文件信息不能为空");

        String originalUrl = fileInfo.get("originalUrl").toString();
        Query query = new Query();
        query.addCriteria(Criteria.where("originalUrl").is(originalUrl));
//        Document fileDoc = mongoTemplate.findOne(query, Document.class, fileCollName);
//        if (!Objects.isNull(fileDoc) && fileDoc.containsKey("newUrl")) {
//            return fileDoc.getString("newUrl");
//        }

        byte[] bytes;
        try {
            bytes = IOUtils.toByteArray(file.getInputStream());
        } catch (IOException e) {
            log.error("上传文件异常", e);
            throw new RuntimeException(e);
        }
        String originalFilename = file.getOriginalFilename();
        String fileSuffix = FastDFSClient.obtainFileSuffix(originalFilename);
        if (StringUtils.isEmpty(fileSuffix)) {
            fileSuffix = FastDFSClient.obtainFileSuffix(originalUrl);
        }
        NameValuePair[] nameValuePairs = new NameValuePair[5];
        nameValuePairs[0] = new NameValuePair("originalUrl", originalUrl);
        nameValuePairs[1] = new NameValuePair("fileName", originalFilename);
        nameValuePairs[2] = new NameValuePair("contentType", file.getContentType());
        nameValuePairs[3] = new NameValuePair("size", Long.toString(file.getSize()));
        nameValuePairs[4] = new NameValuePair("uploadDate", Long.toString(new Date().getTime()));
        String newUrl = fastDFSClient.uploadFile(bytes, fileSuffix, nameValuePairs);
        fileInfo.put("newUrl", newUrl);

        if (fileInfo.containsKey("replaceUrl")) {
            String replaceKey = fileInfo.get("replaceUrl").toString();
            fileInfo.put(replaceKey, newUrl);
        }
        mongoTemplate.remove(query, fileCollName);
        mongoTemplate.insert(fileInfo, fileCollName);
        return newUrl;
    }

    @Override
    public Boolean canDownload(ObjectId fileId) {
        Assert.notNull(fileId, "参数不能为空");

        String ipAddr = IpUtils.getIpAddr();
        log.info("canDownload fileId:" + fileId + " ip:" + ipAddr);

        Criteria c = Criteria.where("fileId").is(fileId).and("ip").is(ipAddr);


        Query query = new Query();
        query.addCriteria(c);
        query.with(Sort.by(Sort.Order.desc("_id")));
        Document one = mongoTemplate.findOne(query, Document.class, downloadVerifyLogCollName);
        if (Objects.isNull(one)) {
            return false;
        }
        return new Date().before(one.getDate("expireAt"));
    }

    @Override
    @Synchronized
    @Transactional
    public String sendVerifyCode(ObjectId fileId, String phone) {
        Assert.notNull(fileId, "参数不能为空");
        Assert.hasText(phone, "手机号不能为空");
        Assert.isTrue(RegularUtils.isMobile(phone), "手机号格式错误");

        Criteria c = Criteria.where("phone").is(phone);
        Query query = new Query();
        query.addCriteria(c);
        query.with(Sort.by(Sort.Order.desc("_id")));
        Document one = mongoTemplate.findOne(query, Document.class, sendCodeLogCollName);
        if (Objects.nonNull(one) && Objects.nonNull(one.getDate("sendDate"))
                && DateUtils.addMinutes(new Date(), 1).before(one.getDate("sendDate"))) {
            throw new RuntimeException("1分钟后重试");
        }

        String code = MspSmsUtils.creatCode(6);
        Document doc = new Document();
        doc.put("fileId", fileId);
        doc.put("phone", phone);
        doc.put("code", code.toLowerCase());
        doc.put("sendDate", new Date());
        doc.put("expireAt", DateUtils.addMinutes(new Date(), 10));
        mongoTemplate.insert(doc, sendCodeLogCollName);

        return mspSmsUtils.sendSms(String.format(smsVerifyCodeTemp, code), phone);
    }

    @Override
    public void downloadVerifyLog(ObjectId fileId, DownloadLogReq downloadLogReq) {
        Assert.notNull(fileId, "参数不能为空");
        Assert.notNull(downloadLogReq, "参数不能为空");
        Assert.hasText(downloadLogReq.getName(), "姓名不能为空");
        Assert.hasText(downloadLogReq.getCompany(), "公司不能为空");
        Assert.hasText(downloadLogReq.getPosts(), "职位不能为空");
        Assert.hasText(downloadLogReq.getMailbox(), "邮箱不能为空");
        Assert.hasText(downloadLogReq.getPhone(), "手机号不能为空");
        Assert.hasText(downloadLogReq.getVerifyCode(), "验证码不能为空");

        Assert.isTrue(RegularUtils.isMobile(downloadLogReq.getPhone()), "手机号格式错误");
        Assert.isTrue(RegularUtils.isEmail(downloadLogReq.getMailbox()), "邮箱格式错误");
        Assert.isTrue(RegularUtils.isPassword(downloadLogReq.getVerifyCode()), "验证码格式错误");

        Criteria c = Criteria.where("code").is(downloadLogReq.getVerifyCode().toLowerCase());
        Query query = new Query();
        query.addCriteria(c);
        query.with(Sort.by(Sort.Order.desc("_id")));
        Document one = mongoTemplate.findOne(query, Document.class, sendCodeLogCollName);
        if (Objects.isNull(one)) {
            throw new RuntimeException("验证码错误");
        }
        if (!downloadLogReq.getPhone().equals(one.getString("phone"))) {
            throw new RuntimeException("手机号错误");
        }
        if (!fileId.equals(one.getObjectId("fileId"))) {
            throw new RuntimeException("手机号错误");
        }
        if (new Date().after(one.getDate("expireAt"))) {
            throw new RuntimeException("验证码已失效");
        }

        String ipAddr = IpUtils.getIpAddr();
        log.info("downloadVerifyLog fileId:" + fileId + " ip:" + ipAddr);

        Map<String, Object> downloadLog = BeanMapUtils.beanToMap(downloadLogReq);
        downloadLog.put("fileId", fileId);
        downloadLog.put("ip", ipAddr);
        downloadLog.put("expireAt", DateUtils.addDays(new Date(), 7));
        downloadLog.put("addDate", new Date());
        mongoTemplate.insert(downloadLog, downloadVerifyLogCollName);
    }

    @Override
    public byte[] download(ObjectId fileId, String filePath) {
        if (StringUtils.isEmpty(fileId) || StringUtils.isEmpty(filePath)) {
            throw new RuntimeException("下载参数错误");
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        Boolean can = canDownload(fileId);
        if (!can) {
            throw new RuntimeException("通过登记验证后再下载");
        }
        return fastDFSClient.downloadFile(filePath);
    }

    @Override
    public void download(ObjectId fileId, String filePath, HttpServletResponse response) throws IOException {
        if (StringUtils.isEmpty(fileId) || StringUtils.isEmpty(filePath)) {
            throw new RuntimeException("下载参数错误");
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        Boolean can = canDownload(fileId);
        if (!can) {
            throw new RuntimeException("通过登记验证后再下载");
        }

        log.info("canDownload filePath:" + filePath);
        // 原文档下载
        boolean originalDownload = false;
        Query q = new Query();
        q.addCriteria(Criteria.where("transedFilePath").is(filePath));
        List<Document> fileDocs = mongoTemplate.find(q, Document.class, fileCollName);
        if (!CollectionUtils.isEmpty(fileDocs)) {
            Document document = fileDocs.get(0);
            Document secretLevel = document.get("secretLevel", Document.class);
            if (!Objects.isNull(secretLevel)) {
                // 5c37f876900add501a1414f9 原文档浏览下载
                log.info("canDownload filePath:" + filePath + " 原文档浏览下载");
                originalDownload = "5c37f876900add501a1414f9".equals(secretLevel.getString("cid"));
            }
        }

        final ServletOutputStream out = response.getOutputStream();
        final boolean hasWatermark = !originalDownload;

        fastDFSClient.downloadFile(filePath, (l, bytes, i) -> {
            try {
                InputStream ins = new ByteArrayInputStream(bytes);
                if (hasWatermark) {
                    PdfWaterMarkParam pdfWaterMarkParam = new PdfWaterMarkParam();
                    pdfWaterMarkParam.setWaterMarkName("AsiaInfo Confidential");
                    pdfWaterMarkParam.setWaterMarkFont(54);
                    pdfWaterMarkParam.setFillOpacity(0.2f);

                    PdfWaterMarkUtil.waterMark(pdfWaterMarkParam, ins, out);
                } else {
                    IOUtils.copy(ins, out);
                }
            } catch (Exception e) {
                throw new RuntimeException("文件下载异常");
            }
            return 0;
        });
    }

    @Override
    public String storeImage(byte[] imageBytes) {
        try {
            // 计算MD5
            String md5 = DigestUtils.md5DigestAsHex(imageBytes);
            
            // 检查是否已存在相同MD5的图片
            ImageInfo existingImage = getImageInfoByMd5(md5);
            if (existingImage != null) {
                return existingImage.getStorePath();
            }
            
            // 存储图片
            String storePath = fastDFSClient.uploadFile(imageBytes, "jpg", new NameValuePair[0]);
            
            // 保存图片信息到数据库
            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setMd5(md5);
            imageInfo.setStorePath(storePath);
            imageInfo.setCreateTime(new Date());
            imageInfo.setUpdateTime(new Date());
            mongoTemplate.save(imageInfo, IMAGE_INFO_COLLECTION);
            
            return storePath;
        } catch (Exception e) {
            log.error("Failed to store image", e);
            throw new RuntimeException("Failed to store image", e);
        }
    }

    @Override
    public ImageInfo getImageInfoByMd5(String md5) {
        Query query = new Query(Criteria.where("md5").is(md5));
        return mongoTemplate.findOne(query, ImageInfo.class, IMAGE_INFO_COLLECTION);
    }

    private void insertSysPageField(List<TeSysPageField> sysPageFields) {
        Query query = new Query();
        query.addCriteria(Criteria.where("file.cid").is(sysPageFields.get(0).getFile().getCid()));
        mongoTemplate.remove(query, pageCollName);

        mongoTemplate.insert(sysPageFields, pageCollName);
    }

    private void insertSysPageFile(LinkedHashMap<String, Object> file) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(file.get("_id").toString()));
        mongoTemplate.remove(query, fileCollName);
        mongoTemplate.insert(file, fileCollName);
    }

    private void dealFile(Document document, Map<ObjectId, List<Map>> foldFileMap) {

        String fileFoldId = document.get("value", String.class);
        List<Map> files = foldFileMap.get(new ObjectId(fileFoldId));

        if (!CollectionUtils.isEmpty(files)) {
            document.put("files", files);
        }
        if (document.containsKey("children")) {
            List<Document> children = document.get("children", List.class);
            if (!CollectionUtils.isEmpty(children)) {
                for (Document child : children) {
                    dealFile(child, foldFileMap);
                }
            }
        }
    }

    private Document obtainFolder(Document document, String fileFolderDefId) {
        if (Objects.isNull(document) || StringUtils.isEmpty(fileFolderDefId)) {
            return null;
        }
        String value = document.getString("value");
        if (fileFolderDefId.equals(value)) {
            return document;
        }
        if (!document.containsKey("children")) {
            return null;
        }
        List<Document> children = document.getList("children", Document.class);
        for (Document child : children) {
            Document doc = obtainFolder(child, fileFolderDefId);
            if (!CollectionUtils.isEmpty(doc)) {
                return doc;
            }
        }
        return null;
    }

    private Set<String> obtainFolderAndChildIds(Document document) {
        Set<String> ids = new HashSet<>();
        if (CollectionUtils.isEmpty(document)) {
            return ids;
        }
        String value = document.getString("value");
        ids.add(value);
        if (document.containsKey("children")) {
            List<Document> children = document.getList("children", Document.class);
            for (Document child : children) {
                ids.addAll(obtainFolderAndChildIds(child));
            }
        }
        return ids;
    }
}
